<div class="flex flex-col w-full  gap-2  md:p-4 p-2 bg-white rounded-2xl">

  <!-- Header Actions -->
  <div class="gap-5 flex">

    <button
      class="button-base"
      [class.button-primary]="activeButton === 'new_order'"
      [class.button-secondary]="activeButton !== 'new_order'"
      (click)="setActiveButton('new_order')">
      <span class="button-text text-sm">{{ 'new_order' | translate }}</span>
    </button>

    <button
      class="button-base"
      [class.button-primary]="activeButton === 'favorite'"
      [class.button-secondary]="activeButton !== 'favorite'"
      (click)="setActiveButton('favorite')">
      <span class="button-text text-sm">{{ 'favorite' | translate }}</span>
    </button>

    <button
      class="button-base"
      [class.button-primary]="activeButton === 'auto_subscription'"
      [class.button-secondary]="activeButton !== 'auto_subscription'"
      (click)="setActiveButton('auto_subscription')">
      <span class="button-text text-sm">{{ 'auto_subscription' | translate }}</span>
    </button>

  </div>


  <!-- Order Success Component - Only shown when order is successful -->
  <app-order-success *ngIf="isOrderSuccess" [order]="createdOrder" (newOrder)="resetOrderState()"></app-order-success>

  <!-- Search -->
  <div>
    <div class="relative w-full border-none">
      <i class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 ">
        <fa-icon [icon]='["fas", "search"]'></fa-icon>
      </i>
      <input placeholder="Tìm kiếm theo tên hoặc ID" (input)="onSearchInput($event)" (click)="onSearchClick()"
        (keydown)="handleSearchKeydown($event)" [(ngModel)]="searchTerm"
        class="w-full border-none bg-[#f5f7fc] rounded-xl pl-14 pr-[22px] py-4 text-[#586280] text-sm" />

      <!-- Autocomplete Results -->
      <app-service-autocomplete
        [services]="searchResults"
        [isVisible]="showAutocomplete"
        (selected)="onServiceSelected($event)"
        (visibilityChange)="onAutocompleteVisibilityChange($event)">
      </app-service-autocomplete>
    </div>
  </div>

  <!-- Category - Hidden when in favorites mode -->
  <div class="" *ngIf="activeButton !== 'favorite'">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'classification' | translate }}</h3>

    <!-- Show dropdown if categories are available -->
    <app-icon-dropdown
      #categoryDropdown
      *ngIf="categories.length > 0"
      [options]="categories"
      [selectedOption]="selectedCategory"
      [customClassDropdown]="'bg-[#f5f7fc] rounded-lg '"
      [customClassButton]="'border'"
      (selected)="onCategorySelected($event)">
    </app-icon-dropdown>

    <!-- Show message if no categories are available -->
    <div *ngIf="categories.length === 0" class="bg-[#f5f7fc] rounded-lg p-4 flex items-center justify-center">
      <p class="text-sm text-[#586280]">{{ 'no_services_available' | translate }}</p>
    </div>
  </div>

  <!-- Service -->
  <div class="">
    <h3 class="text-sm font-semibold mb-2.5">
      <ng-container *ngIf="activeButton === 'favorite'">{{ 'favorite_services' | translate }}</ng-container>
      <ng-container *ngIf="activeButton !== 'favorite'">{{ 'service' | translate }}</ng-container>
    </h3>

    <!-- Show service dropdown if there are services in normal mode -->
    <ng-container *ngIf="activeButton !== 'favorite' && services.length > 0">
      <app-service-dropdown #serviceDropdown
        [options]="services"
        [selectedOption]="selectedService"
        [customClassDropdown]="'bg-[#f5f7fc] rounded-lg '"
        (selected)="onServiceDropdownSelected($event)">
      </app-service-dropdown>
    </ng-container>

    <!-- Show service dropdown if in favorites mode and there are favorite services -->
    <ng-container *ngIf="activeButton === 'favorite' && favoriteServices.length > 0">
      <app-service-dropdown #serviceDropdown
        [options]="services"
        [selectedOption]="selectedService"
        [customClassDropdown]="'bg-[#f5f7fc] rounded-lg '"
        (selected)="onServiceDropdownSelected($event)">
      </app-service-dropdown>
    </ng-container>

    <!-- Show empty state if in favorites mode and no favorite services -->
    <ng-container *ngIf="activeButton === 'favorite' && favoriteServices.length === 0">
      <div class="bg-[#f5f7fc] rounded-lg p-4 flex items-center justify-center">
        <p class="text-sm text-[#586280]">{{ 'no_favorite_services' | translate }}</p>
      </div>
    </ng-container>

    <!-- This section has been removed as we now show all categories by default -->

    <!-- Show message if no services are available -->
    <ng-container *ngIf="activeButton !== 'favorite' && services.length === 0">
      <div class="bg-[#f5f7fc] rounded-lg p-4 flex items-center justify-center">
        <p class="text-sm text-[#586280]">{{ 'no_services_available' | translate }}</p>
      </div>
    </ng-container>
  </div>

  <!-- Link - Hidden when in favorites mode with no favorites or when no services are available -->
  <div class="" *ngIf="(activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'link' | translate }}</h3>
    <input [(ngModel)]="formData.link" class="z-text-base p-4" [placeholder]="selectedService?.sample_link || ''" />
  </div>

  <!-- Comments - Only shown for Comment type services -->
  <div class="" *ngIf="((activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)) && selectedService?.type === 'Custom Comments'">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'comments' | translate }}</h3>
    <textarea
      [(ngModel)]="formData.comments"
      (ngModelChange)="validateQuantityAndCalculateFee()"
      class="z-text-base p-4 w-full min-h-[120px]"
      [ngClass]="{'border-red-500': quantityError}"
      placeholder="{{ 'enter_comments_one_per_line' | translate }}"></textarea>
    <p class="text-sm text-[#586280] mt-2">{{ 'each_line_is_one_comment' | translate }}</p>
    <p class="text-sm text-[#586280] mt-1">{{ 'current_comments_count' | translate }}: {{ getCommentsArray().length }}</p>
    <p *ngIf="quantityError" class="text-sm text-red-500 mt-1">{{ quantityError }}</p>
  </div>

  <!-- Quantity - Hidden when in favorites mode with no favorites or when no services are available -->
  <div class="" *ngIf="((activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)) && selectedService?.type !== 'Custom Comments'">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'quantity' | translate }}</h3>
    <input
      type="number"
      [(ngModel)]="formData.quantity"
      (ngModelChange)="validateQuantityAndCalculateFee()"
      class="z-text-base p-4"
      [ngClass]="{'border-red-500': quantityError}"
      [min]="selectedService?.min || 10"
      [max]="selectedService?.max || 5000000" />
    <p class="text-sm text-[#586280] mt-2">Min: {{ selectedService?.min || 10 }} - Max: {{ selectedService?.max || 5000000 }}</p>
    <p *ngIf="quantityError" class="text-sm text-red-500 mt-1">{{ quantityError }}</p>
  </div>

  <!-- Quantity display for Comment type services (read-only) -->
  <div class="" *ngIf="((activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)) && selectedService?.type === 'Custom Comments'">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'quantity' | translate }}</h3>
    <input
      type="number"
      [ngModel]="formData.quantity"
      class="z-text-base p-4 bg-gray-100"
      readonly
      disabled />
    <p class="text-sm text-[#586280] mt-2">{{ 'quantity_based_on_comments' | translate }}</p>
  </div>

  <!-- Voucher Code -->
  <div class="" *ngIf="(activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)">
    <h3 class="text-sm font-semibold mb-2.5">{{ 'voucher_code' | translate }}</h3>
    <div class="flex gap-2">
      <input
        [(ngModel)]="formData.voucher_code"
        class="z-text-base p-4 flex-grow"
        [ngClass]="{'border-red-500': voucherError, 'border-green-500': voucherApplied}"
        placeholder="{{ 'enter_voucher_code' | translate }}" />
      <button
        *ngIf="formData.voucher_code && !voucherApplied"
        (click)="validateVoucherCode()"
        class="px-4 bg-[var(--primary)] text-white rounded-lg"
        [disabled]="isValidatingVoucher">
        <span *ngIf="!isValidatingVoucher">{{ 'apply' | translate }}</span>
        <span *ngIf="isValidatingVoucher">...</span>
      </button>
      <button
        *ngIf="formData.voucher_code && voucherApplied"
        (click)="clearVoucherCode()"
        class="px-4 bg-red-500 text-white rounded-lg"
        title="{{ 'clear' | translate }}">
        <fa-icon [icon]='["fas", "times"]'></fa-icon>
      </button>
    </div>
    <p *ngIf="voucherError" class="text-sm text-red-500 mt-1">{{ voucherError }}</p>
    <p *ngIf="voucherApplied" class="text-sm text-green-500 mt-1">
      {{ 'voucher_applied' | translate }} (-{{ voucherDiscount }}%)
    </p>
  </div>


  <!-- Submit Button - Hidden when in favorites mode with no favorites or when no services are available -->
  <button *ngIf="(activeButton !== 'favorite' && services.length > 0) || (activeButton === 'favorite' && favoriteServices.length > 0)"
          (click)="onSubmit()"
          [disabled]="isLoading || quantityError !== ''"
          class="btn-submit gap-3"
          [ngClass]="{'opacity-50 cursor-not-allowed': quantityError !== ''}">
    <span *ngIf="!isLoading">{{ 'send' | translate }}</span>
    <span *ngIf="isLoading">{{ 'processing' | translate }}...</span>

    <div class="flex items-center">
      <!-- Show original price with strikethrough if there's a discount -->
      <span *ngIf="discountPercent > 0 || voucherApplied" class="price-original line-through text-gray-500 mr-2">
        {{originalPrice | currencyConvert}}
      </span>
      <!-- Show discount percentage if applicable -->
      <span *ngIf="discountPercent > 0" class="discount-badge bg-red-500 text-white text-xs px-1 py-0.5 rounded mr-2">
        -{{discountPercent}}%
      </span>
      <!-- Show voucher discount if applied -->
      <span *ngIf="voucherApplied" class="discount-badge bg-green-500 text-white text-xs px-1 py-0.5 rounded mr-2">
        -{{voucherDiscount}}%
      </span>
      <!-- Always show the final price -->
      <span class="price-span">{{formData.fee | currencyConvert}}</span>
    </div>
  </button>

  <!-- Error message display -->
  <div *ngIf="errorMessage" class="text-red-500 mt-2 text-center">
    {{ errorMessage }}
  </div>

  <!-- <app-success-order *ngIf="showModal" (close)="closeModal()"></app-success-order> -->
  <!-- <app-platform *ngIf="showModal" (close)="closeModal()"></app-platform> -->
  <!-- <app-resources *ngIf="showModal" (close)="closeModal()"></app-resources> -->

</div>